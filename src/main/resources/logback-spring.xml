<configuration scan="false" scanPeriod="1 seconds" debug="false">
  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <!-- encoder 默认配置为PatternLayoutEncoder -->
    <encoder>
      <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} %X{traceId} %X{userId} %msg%n
      </pattern>
    </encoder>
  </appender>
  <appender name="RollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <!-- 可让每天产生一个日志文件，最多 15 个，自动回滚 -->
    <file>${CATALINA_HOME}/logs/app.log</file>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <fileNamePattern>${CATALINA_HOME}/logs/app-%d{yyyyMMdd}.log.zip</fileNamePattern>
      <maxHistory>15</maxHistory>
    </rollingPolicy>
    <encoder>
      <!-- 日志中默认打印traceId和userId，方便定位问题,异常栈中去掉包含如下字符的行避免打印很多无用的信息-->
      <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%rEx{full,
        java.lang.Thread,
        javassist,
        sun.Reflect,
        org.springframework,
        org.apache,
        org.eclipse.jetty,
        $Proxy,
        java.net,
        java.io,
        jakarta.Servlet,
        org.junit,
        com.mysql,
        com.sun,
        org.mybatis.spring,
        cglib,
        CGLIB,
        java.util.concurrent,
        okhttp,
        org.jboss,
        }%n
      </pattern>
    </encoder>
  </appender>

  <!-- 异步输出日志避免阻塞服务 -->
  <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
    <queueSize>512</queueSize>
    <appender-ref ref="RollingFile"/>
  </appender>

  <!-- 配置基础组件为WARN级别，避免打印过多影响服务自己日志 -->
  <logger name="druid.sql" level="WARN"/>
  <logger name="org.hibernate" level="WARN"/>
  <logger name="org.springframework" level="WARN"/>
  <logger name="org.apache" level="WARN"/>
  <logger name="com.github.trace" level="WARN"/>
  <logger name="com.fxiaoke.j4log.J4LogFactory" level="WARN"/>
  <logger name="RocketmqRemoting" level="WARN"/>
  <logger name="c.g.autoconf.task.PersistentTask" level="WARN"/>
  <!--屏蔽UC 组件的日志消息-->
  <logger name="com.facishare.uc" level="WARN"/>
  <!-- 屏蔽 PDFBox 的非 Error 日志 -->
  <logger name="org.apache.pdfbox" level="ERROR"/>
  <logger name="org.apache.fontbox" level="ERROR"/>
  <logger name="org.apache.pdfbox.contentstream.operator.state.SetGraphicsStateParameters"
    level="OFF" additivity="false"/>

  <root level="info">
    <appender-ref ref="ASYNC"/>
    <appender-ref ref="STDOUT"/>
  </root>
</configuration>
