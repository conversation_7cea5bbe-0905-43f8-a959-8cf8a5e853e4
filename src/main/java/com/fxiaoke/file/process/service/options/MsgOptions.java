package com.fxiaoke.file.process.service.options;

import com.aspose.email.MailMessage;
import com.fxiaoke.file.process.client.ArkClient;
import com.fxiaoke.file.process.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.domain.constants.FileType;
import com.fxiaoke.file.process.domain.constants.VisCompDetail;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.domain.model.MarkdownImgInfo;
import com.fxiaoke.file.process.domain.model.ToMdParams;
import com.fxiaoke.file.process.domain.model.ToMdResult;
import com.fxiaoke.file.process.domain.model.Usages;
import com.fxiaoke.file.process.domain.model.VisCompRes;
import com.fxiaoke.file.process.service.FileService;
import com.fxiaoke.file.process.service.options.msg.MsgConverter;
import com.fxiaoke.file.process.service.options.msg.MsgLoader;
import com.fxiaoke.file.process.service.options.msg.MsgProcessingResult;
import com.fxiaoke.file.process.utils.MarkDownUtils;

import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 用于将 MSG 文件转换为 Markdown 格式的选项类。
 * 
 * 重构后的版本特点：
 * 1. 主流程清晰：toTextMarkdown 和 toImageMarkdown 方法职责单一
 * 2. 职责分离：使用专门的处理器类处理不同的业务逻辑
 * 3. 配置优化：使用优化的 MsgLoadOptions 配置
 * 4. 错误处理：统一的异常处理机制
 * 5. 可测试性：各个组件独立，便于单元测试
 */
@Component
@Slf4j
public class MsgOptions extends DocOptions {

  private static final String MODULE = "MsgOptions";
  
  private final MsgConverter msgConverter;

  public MsgOptions(ArkClient arkClient,
      FileService fileService,
      CmsPropertiesConfig cmsPropertiesConfig) {
    super(arkClient, fileService, cmsPropertiesConfig);
    this.msgConverter = new MsgConverter(fileService);
  }

  /**
   * 文本模式转换：快速转换，不进行OCR处理
   * 
   * 处理流程：
   * 1. 加载MSG文件（使用优化的配置）
   * 2. 转换为文本模式Markdown（邮件头 + 纯文本正文 + 附件列表）
   * 3. 保存Markdown文件
   */
  @Override
  protected ToMdResult toTextMarkdown(ToMdParams params) {
    try {
      // 1. 加载MSG文件
      MailMessage mailMessage = MsgLoader.loadMsgFile(params.getFilePath());
      
      // 2. 转换为文本模式Markdown
      MsgProcessingResult result = msgConverter.convertToTextMarkdown(mailMessage, params);
      
      // 3. 保存Markdown文件
      Path mdFilePath = saveMarkdownFile(params, result.getCompleteMarkdown());
      
      return new ToMdResult(1, params.getFileName(), mdFilePath, null);

    } catch (BaseException e) {
      throw e;
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".toTextMarkdown", params);
    }
  }

  /**
   * 图像模式转换：完整转换，包含OCR处理
   * 
   * 处理流程：
   * 1. 加载MSG文件（使用优化的配置）
   * 2. 转换为图像模式Markdown（邮件头 + HTML正文转换 + 内嵌图片提取 + 附件保存）
   * 3. 保存初始Markdown文件
   * 4. OCR处理内嵌图片（如果存在）
   * 5. 替换图片标签为OCR结果
   */
  @Override
  protected ToMdResult toImageMarkdown(ToMdParams params) {
    try {
      // 1. 加载MSG文件
      MailMessage mailMessage = MsgLoader.loadMsgFile(params.getFilePath());
      
      // 2. 转换为图像模式Markdown
      MsgProcessingResult result = msgConverter.convertToImageMarkdown(mailMessage, params);
      
      // 3. 保存初始Markdown文件
      Path mdFilePath = saveMarkdownFile(params, result.getCompleteMarkdown());
      log.info("MSG To Markdown Process, Total Embedded Images: {}", result.getEmbeddedImages().size());
      
      // 4. 处理OCR（如果有内嵌图片）
      Usages usages = null;
      Path finalMdFilePath = mdFilePath;
      
      if (result.hasEmbeddedImages()) {
        List<VisCompRes<MarkdownImgInfo>> visCompRes = processOcrForImages(params, result.getEmbeddedImages());
        usages = accumulateUsage(params.getVisCompConfig(), visCompRes);
        finalMdFilePath = MarkDownUtils.replaceImageTags(mdFilePath, visCompRes, FileType.MSG);
        log.info("MSG To Markdown End, Final Md File: {}", finalMdFilePath);
      }

      return new ToMdResult(1, params.getFileName(), finalMdFilePath, usages);
    } catch (BaseException e) {
      throw e;
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".toImageMarkdown", params);
    }
  }

  /**
   * 保存Markdown文件
   */
  private Path saveMarkdownFile(ToMdParams params, String content) throws Exception {
    Path mdFilePath = params.generatMdFilePath();
    Files.writeString(mdFilePath, content, StandardCharsets.UTF_8);
    return mdFilePath;
  }

  /**
   * 处理OCR识别
   */
  private List<VisCompRes<MarkdownImgInfo>> processOcrForImages(ToMdParams params, List<MarkdownImgInfo> embeddedImages) {
    var visCompConfig = params.getVisCompConfig();
    visCompConfig.setDetail(VisCompDetail.LOW);
    return visCompRes(visCompConfig, embeddedImages);
  }
}
