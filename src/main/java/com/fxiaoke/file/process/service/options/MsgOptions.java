package com.fxiaoke.file.process.service.options;

import com.aspose.email.AlternateView;
import com.aspose.email.AlternateViewCollection;
import com.aspose.email.Attachment;
import com.aspose.email.AttachmentCollection;
import com.aspose.email.LinkedResource;
import com.aspose.email.LinkedResourceCollection;
import com.aspose.email.MailAddress;
import com.aspose.email.MailAddressCollection;
import com.aspose.email.MailMessage;
import com.aspose.email.MsgLoadOptions;
import com.fxiaoke.file.process.client.ArkClient;
import com.fxiaoke.file.process.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.domain.constants.FileType;
import com.fxiaoke.file.process.domain.constants.VisCompDetail;
import com.fxiaoke.file.process.domain.exception.BaseException;
import com.fxiaoke.file.process.domain.model.MarkdownImgInfo;
import com.fxiaoke.file.process.domain.model.ToMdParams;
import com.fxiaoke.file.process.domain.model.ToMdResult;
import com.fxiaoke.file.process.domain.model.Usages;
import com.fxiaoke.file.process.domain.model.VisCompConfig;
import com.fxiaoke.file.process.domain.model.VisCompRes;
import com.fxiaoke.file.process.service.FileService;
import com.fxiaoke.file.process.utils.MarkDownUtils;
import com.vladsch.flexmark.html2md.converter.FlexmarkHtmlConverter;
import com.vladsch.flexmark.html2md.converter.HtmlNodeRendererFactory;
import com.vladsch.flexmark.util.data.MutableDataSet;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Component;

/**
 * 用于将 msg 文件转换为 Markdown 格式的选项类。
 */
@Component
@Slf4j(topic = "MsgOptions")
public class MsgOptions extends DocOptions {

  private static final String MODULE = "MsgOptions";
  private static final String MARKDOWN_CONTENT_HEADER = "## 正文内容";
  private static final String MARKDOWN_ATTACHMENT_HEADER = "## 附件";
  private static final String DEFAULT_SUBJECT = "无主题";
  private static final String DEFAULT_SENDER = "未知";
  private static final String DEFAULT_DATE = "未知";
  private static final String UNNAMED_ATTACHMENT = "未命名附件";
  private static final String EMBEDDED_IMAGE_PREFIX = "embedded_";
  private static final String ATTACHMENT_PREFIX = "attachment_";
  private static final String CID_PREFIX = "cid:";
  
  private static final Set<String> SUPPORTED_IMAGE_EXTENSIONS = Set.of(
      "jpg", "jpeg", "png", "gif", "webp", "bmp"
  );
  
  private static final Map<String, String> MEDIA_TYPE_TO_EXTENSION = Map.of(
      "image/jpeg", "jpg",
      "image/jpg", "jpg", 
      "image/gif", "gif",
      "image/webp", "webp"
  );

  public MsgOptions(ArkClient arkClient,
      FileService fileService,
      CmsPropertiesConfig cmsPropertiesConfig) {
    super(arkClient, fileService, cmsPropertiesConfig);
  }

  @Override
  protected ToMdResult toTextMarkdown(ToMdParams params) {
    try {
      MailMessage mailMessage = loadMsgFile(params.getFilePath());
      StringBuilder markdownBuilder = buildEmailHeader(mailMessage);
      
      appendBodyContent(markdownBuilder, mailMessage.getBody());
      appendAttachmentsTextMode(markdownBuilder, mailMessage.getAttachments());
      
      Path mdFilePath = saveMarkdownFile(params, markdownBuilder.toString());
      return new ToMdResult(1, params.getFileName(), mdFilePath, null);

    } catch (BaseException e) {
      throw e;
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".toTextMarkdown", params);
    }
  }

  @Override
  protected ToMdResult toImageMarkdown(ToMdParams params) {
    try {
      MailMessage mailMessage = loadMsgFile(params.getFilePath());
      Path outputDir = Paths.get(params.getParentDir());
      
      StringBuilder markdownBuilder = buildEmailHeader(mailMessage);
      List<MarkdownImgInfo> embeddedImages = new ArrayList<>();
      
      String htmlContent = processHtmlBodyWithEmbeddedImages(mailMessage, outputDir, embeddedImages);
      String bodyMarkdown = convertHtmlToMarkdown(htmlContent, params);
      markdownBuilder.append("\n").append(MARKDOWN_CONTENT_HEADER).append("\n").append(bodyMarkdown);
      
      appendAttachments(markdownBuilder, mailMessage.getAttachments(), outputDir);
      
      Path mdFilePath = saveMarkdownFile(params, markdownBuilder.toString());
      log.info("MSG To Markdown Process, Total Embedded Images: {}", embeddedImages.size());
      
      List<VisCompRes<MarkdownImgInfo>> visCompRes = processOcr(params, embeddedImages);
      Usages usages = accumulateUsage(params.getVisCompConfig(), visCompRes);
      
      Path finalMdFilePath = MarkDownUtils.replaceImageTags(mdFilePath, visCompRes, FileType.MSG);
      log.info("MSG To Markdown End, Final Md File: {}", finalMdFilePath);

      return new ToMdResult(1, params.getFileName(), finalMdFilePath, usages);

    } catch (BaseException e) {
      throw e;
    } catch (Exception e) {
      throw new BaseException(e, 500, MODULE + ".toImageMarkdown", params);
    }
  }

  private MailMessage loadMsgFile(Path filePath) {
    try {
      MsgLoadOptions loadOptions = new MsgLoadOptions();
      loadOptions.setPreserveTnefAttachments(true);
      return MailMessage.load(filePath.toString(), loadOptions);
    } catch (Exception e) {
      throw new BaseException(e, 400, MODULE + ".loadMsgFile",
          "MSG file encrypted or corrupted", filePath);
    }
  }

  private StringBuilder buildEmailHeader(MailMessage mailMessage) {
    StringBuilder sb = new StringBuilder();
    
    sb.append("# ").append(Optional.ofNullable(mailMessage.getSubject()).orElse(DEFAULT_SUBJECT)).append("\n");
    sb.append("**发件人**: ").append(formatMailAddress(mailMessage.getFrom())).append("\n");
    sb.append("**收件人**: ").append(formatMailAddressCollection(mailMessage.getTo())).append("\n");
    
    Optional.ofNullable(mailMessage.getCC())
        .filter(cc -> cc.size() > 0)
        .ifPresent(cc -> sb.append("**抄送**: ").append(cc).append("\n"));
        
    sb.append("**时间**: ").append(formatDate(mailMessage.getDate())).append("\n");
    
    return sb;
  }
  
  private String formatMailAddress(MailAddress address) {
    return address != null ? address.toString() : DEFAULT_SENDER;
  }
  
  private String formatMailAddressCollection(MailAddressCollection addresses) {
    return addresses != null ? addresses.toString() : DEFAULT_SENDER;
  }
  
  private String formatDate(java.util.Date date) {
    return date != null ? date.toString() : DEFAULT_DATE;
  }
  
  private void appendBodyContent(StringBuilder markdownBuilder, String textBody) {
    if (textBody != null && !textBody.trim().isEmpty()) {
      markdownBuilder.append("\n").append(MARKDOWN_CONTENT_HEADER).append("\n").append(textBody);
    }
  }
  
  private Path saveMarkdownFile(ToMdParams params, String content) throws Exception {
    Path mdFilePath = params.generatMdFilePath();
    Files.writeString(mdFilePath, content, StandardCharsets.UTF_8);
    return mdFilePath;
  }
  
  private List<VisCompRes<MarkdownImgInfo>> processOcr(ToMdParams params, List<MarkdownImgInfo> embeddedImages) {
    VisCompConfig visCompConfig = params.getVisCompConfig();
    visCompConfig.setDetail(VisCompDetail.LOW);
    List<VisCompRes<MarkdownImgInfo>> visCompRes = visCompRes(visCompConfig, embeddedImages);
    
    Usages usages = accumulateUsage(visCompConfig, visCompRes);
    log.info("MSG To Markdown Process, Total Usages: {}", usages);
    
    return visCompRes;
  }
  
  private String convertHtmlToMarkdown(String htmlContent, ToMdParams params) {
    MutableDataSet options = createMutableDataSet(false);
    HtmlNodeRendererFactory rendererFactory = createHtmlNodeRendererFactory(params);
    return convertHtmlToMarkdown(htmlContent, options, rendererFactory);
  }

  private String processHtmlBodyWithEmbeddedImages(MailMessage mailMessage,
      Path outputDir, List<MarkdownImgInfo> embeddedImages) throws Exception {

    String htmlBody = Optional.ofNullable(mailMessage.getHtmlBody())
        .filter(body -> !body.trim().isEmpty())
        .orElse(Optional.ofNullable(mailMessage.getBody()).orElse(""));

    if (htmlBody.isEmpty()) {
      return htmlBody;
    }

    AlternateViewCollection views = mailMessage.getAlternateViews();
    EmbeddedImageProcessor processor = new EmbeddedImageProcessor(outputDir, embeddedImages);
    
    return processor.processAlternateViews(views, htmlBody);
  }
  
  private class EmbeddedImageProcessor {
    private final Path outputDir;
    private final List<MarkdownImgInfo> embeddedImages;
    private int embeddedIndex = 1;
    
    EmbeddedImageProcessor(Path outputDir, List<MarkdownImgInfo> embeddedImages) {
      this.outputDir = outputDir;
      this.embeddedImages = embeddedImages;
    }
    
    String processAlternateViews(AlternateViewCollection views, String htmlBody) throws Exception {
      String processedHtml = htmlBody;
      
      for (AlternateView view : views) {
        if (isHtmlView(view)) {
          processedHtml = processLinkedResources(view.getLinkedResources(), processedHtml);
        }
      }
      
      return processedHtml;
    }
    
    private boolean isHtmlView(AlternateView view) {
      return "text/html".equals(view.getContentType().getMediaType());
    }
    
    private String processLinkedResources(LinkedResourceCollection resources, String htmlBody) throws Exception {
      String processedHtml = htmlBody;
      
      for (LinkedResource resource : resources) {
        if (isImageResource(resource)) {
          processedHtml = processImageResource(resource, processedHtml);
        }
      }
      
      return processedHtml;
    }
    
    private String processImageResource(LinkedResource resource, String htmlBody) throws Exception {
      String contentId = resource.getContentId();
      String fileName = generateFileName(resource);
      Path imagePath = outputDir.resolve(fileName);
      
      saveResourceToFile(resource, imagePath);
      
      if (contentId != null) {
        htmlBody = htmlBody.replace(CID_PREFIX + contentId, fileName);
      }
      
      embeddedImages.add(createMarkdownImgInfo(imagePath, fileName));
      
      return htmlBody;
    }
    
    private String generateFileName(LinkedResource resource) {
      return String.format("%s%d.%s", EMBEDDED_IMAGE_PREFIX, embeddedIndex++, getImageExtension(resource));
    }
    
    private void saveResourceToFile(LinkedResource resource, Path imagePath) throws Exception {
      try (InputStream stream = resource.getContentStream()) {
        Files.copy(stream, imagePath, StandardCopyOption.REPLACE_EXISTING);
      }
    }
    
    private MarkdownImgInfo createMarkdownImgInfo(Path imagePath, String fileName) {
      MarkdownImgInfo imgInfo = new MarkdownImgInfo();
      imgInfo.setImagePath(imagePath);
      imgInfo.setOriginalTag(String.format("![%s](%s)", fileName, fileName));
      return imgInfo;
    }
  }

  private void appendAttachments(StringBuilder markdownBuilder, AttachmentCollection attachments, Path outputDir) {
    if (attachments == null || attachments.size() == 0) {
      return;
    }

    markdownBuilder.append("\n").append(MARKDOWN_ATTACHMENT_HEADER).append("\n");
    AttachmentProcessor processor = new AttachmentProcessor(outputDir);
    processor.processAttachments(attachments, markdownBuilder);
  }

  private void appendAttachmentsTextMode(StringBuilder markdownBuilder, AttachmentCollection attachments) {
    if (attachments == null || attachments.size() == 0) {
      return;
    }

    markdownBuilder.append("\n").append(MARKDOWN_ATTACHMENT_HEADER).append("\n");
    
    for (Attachment attachment : attachments) {
      String originalName = getAttachmentName(attachment);
      markdownBuilder.append(String.format("- %s\n", originalName));
    }
  }
  
  private class AttachmentProcessor {
    private final Path outputDir;
    private int attachmentIndex = 1;
    
    AttachmentProcessor(Path outputDir) {
      this.outputDir = outputDir;
    }
    
    void processAttachments(AttachmentCollection attachments, StringBuilder markdownBuilder) {
      for (Attachment attachment : attachments) {
        processAttachment(attachment, markdownBuilder);
      }
    }
    
    private void processAttachment(Attachment attachment, StringBuilder markdownBuilder) {
      String originalName = getAttachmentName(attachment);
      String fileName = generateAttachmentFileName(originalName);
      Path attachmentPath = outputDir.resolve(fileName);
      
      attachment.save(attachmentPath.toString());
      appendAttachmentToMarkdown(markdownBuilder, originalName, fileName);
    }
    
    private String generateAttachmentFileName(String originalName) {
      return String.format("%s%d_%s", ATTACHMENT_PREFIX, attachmentIndex++, originalName);
    }
    
    private void appendAttachmentToMarkdown(StringBuilder markdownBuilder, String originalName, String fileName) {
      if (isImageFile(originalName)) {
        markdownBuilder.append(String.format("- %s ![%s](%s)\n", originalName, originalName, fileName));
      } else {
        markdownBuilder.append(String.format("- %s\n", originalName));
      }
    }
  }
  
  private String getAttachmentName(Attachment attachment) {
    String originalName = attachment.getName();
    if (originalName == null || originalName.trim().isEmpty()) {
      return UNNAMED_ATTACHMENT;
    }
    return originalName;
  }

  private boolean isImageResource(LinkedResource resource) {
    String mediaType = resource.getContentType().getMediaType();
    return mediaType != null && mediaType.startsWith("image/");
  }

  private String getImageExtension(LinkedResource resource) {
    String mediaType = resource.getContentType().getMediaType();
    if (mediaType != null) {
      return MEDIA_TYPE_TO_EXTENSION.getOrDefault(mediaType.toLowerCase(), "png");
    }
    return "png";
  }

  private static boolean isImageFile(String fileName) {
    if (fileName == null) return false;
    String ext = FilenameUtils.getExtension(fileName).toLowerCase();
    return SUPPORTED_IMAGE_EXTENSIONS.contains(ext);
  }

  private MutableDataSet createMutableDataSet(boolean preserveLineBreaks) {
    MutableDataSet options = new MutableDataSet();
    options.set(FlexmarkHtmlConverter.SKIP_HEADING_1, false);
    options.set(FlexmarkHtmlConverter.SKIP_HEADING_2, false);
    options.set(FlexmarkHtmlConverter.SKIP_ATTRIBUTES, true);
    options.set(FlexmarkHtmlConverter.SKIP_FENCED_CODE, false);

    if (preserveLineBreaks) {
      options.set(FlexmarkHtmlConverter.BR_AS_EXTRA_BLANK_LINES, false);
    }

    return options;
  }

  private String convertHtmlToMarkdown(String htmlContent, MutableDataSet options, HtmlNodeRendererFactory rendererFactory) {
    var builder = FlexmarkHtmlConverter.builder(options);
    if (rendererFactory != null) {
      builder.htmlNodeRendererFactory(rendererFactory);
    }
    return builder.build().convert(htmlContent);
  }

  private HtmlNodeRendererFactory createHtmlNodeRendererFactory(ToMdParams params) {
    return dataHolder -> new HtmlOptionsNodeRenderer(
        params.getEa(), 
        params.getEmployeeId(),
        params.getParentDir(), 
        fileService
    );
  }
}
