package com.fxiaoke.file.process.service.options;

import com.fxiaoke.file.process.client.ArkClient;
import com.fxiaoke.file.process.config.CmsPropertiesConfig;
import com.fxiaoke.file.process.domain.model.ToMdParams;
import com.fxiaoke.file.process.domain.model.ToMdResult;
import com.fxiaoke.file.process.service.FileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 用于将 msg 文件转换为 Markdown 格式的选项类。
 */
@Component
@Slf4j(topic = "MsgOptions")
public class MsgOptions extends DocOptions{

  public MsgOptions(ArkClient arkClient,
      FileService fileService,
      CmsPropertiesConfig cmsPropertiesConfig) {
    super(arkClient, fileService, cmsPropertiesConfig);
  }

  @Override
  ToMdResult toImageMarkdown(ToMdParams params) {
    return null;
  }

  @Override
  ToMdResult toTextMarkdown(ToMdParams params) {
    return null;
  }
}
